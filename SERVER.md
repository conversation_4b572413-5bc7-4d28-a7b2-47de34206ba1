## 服务端安装部署

### 1. 下载本项目到本地指定路径

使用 git clone 或下载 zip 解压

```bash
git clone https://github.com/zhzhzhy/NinjiaTag-backend.git /opt/1panel/apps/NinjiaTag-backend --depth=1
```

### 2. 运行 [Anisette Server](https://github.com/Dadoum/anisette-v3-server)

<!-- ```bash
docker run -d --restart always --name anisette -p 6969:6969 --volume anisette-v3_data:/home/<USER>/.config/anisette-v3/ --network NinjiaTag-network dadoum/anisette-v3-server
``` -->

复制 `docker-compose.yml` 到项目根目录下：

```shell
docker compose up -d
```

### 4.放置服务端 key

将硬件设置步骤中生成的.key 后缀的文件放置在本项目/keys 文件夹下，后续脚本会自动转化

### 5.安装 python3 相关库

#### 基础网络和加密组件

安装 python3，并使用 pip3 安装相关依赖

```
pip3 install aiohttp requests cryptography pycryptodome srp pbkdf2
```

#### 创建 python3 venv 虚拟环境(可选)

如果成功通过[pip3](#基础网络和加密组件)安装完成依赖，该步骤可忽略

`python3 -m venv ./venv/`

##### venv 虚拟环境 pip3 安装相关依赖

```
./venv/bin/pip3 install aiohttp requests cryptography pycryptodome srp pbkdf2 -i https://pypi.tuna.tsinghua.edu.cn/simple
```

在 python3 venv 环境执行
`./venv/bin/python3 request_reports.py`

或执行`python3 request_reports.py`

开始时会提示输入 Apple ID 和密码，然后是 2FA 短信验证，完成后能正常执行位置数据拉取。

### 安装 nodejs

执行以下命令安装 nodejs 和 npm 用于提供后端服务

```bash
# Download and install nvm:
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash
# in lieu of restarting the shell
\. "$HOME/.nvm/nvm.sh"
# Download and install Node.js:
nvm install 22
# Verify the Node.js version:
node -v # Should print "v22.17.1".
nvm current # Should print "v22.17.1".
# Verify npm version:
npm -v # Should print "10.9.2".
```

如果网络有问题可尝试在 nodejs 官网下载安装包进行手动安装，不在本教程范围内，可自行搜索安装教程

完成后在项目目录下执行：

```
npm i
```

安装 node_modules 相关依赖

#### 修改 request_reports.mjs

如果在 python venv 执行，将`request_reports.mjs`文件中

```
python3 request_reports.py
```

修改为

```
./venv/bin/python3 request_reports.py
```

可以通过修改`*/5 * * * *`来修改从 Findmy 服务器获取位置数据的时间间隔，具体参考 Cron 的文档，如`*/5 * * * *`表示每 5 分钟请求一次，不建议修改得太频繁

### 安装 pm2 守护定时执行

长期运行 "server.mjs" 和 "request_reports.mjs" 以保证服务器能定期取回位置数据并存于数据库

#### PM2 安装说明

1. 通过 npm 全局安装

`npm install pm2 -g`

- 验证安装：执行
  "pm2 --version"，输出版本号即安装成功。
- 权限问题（Linux/Mac）：若提示权限不足，可添加
  "sudo" 或执行
  `chmod 777 /usr/local/bin/pm2`授权。

#### PM2 长期运行脚本命令

##### 启动脚本并命名进程

- 运行
  "server.mjs"（数据库查询主服务）：
  `pm2 start server.mjs --name "query-server" --watch`

"--name"：自定义进程名称（便于管理）。
"--watch"：监听文件改动自动重启。

- 运行
  "request_reports.mjs"（抓取位置数据任务）：
  `pm2 start request_reports.mjs --name "report-task" --watch`

##### 长期运行保障措施

1. 保存进程列表
   `pm2 save`
   保存当前运行列表，防止重启后丢失。
2. 设置系统开机自启
   `pm2 startup` # 生成启动脚本
   `sudo pm2 startup systemd  # Linux systemd 系统`
   `pm2 save  # 关联保存的进程列表`
   服务器重启后 PM2 自动恢复进程。
3. 日志管理
   - 查看实时日志：
     `pm2 logs web-server  # 指定进程名`

##### pm2 常用管理命令(部署时可忽略)

- 查看进程状态
  "pm2 list" 显示所有进程及资源占用
- 停止进程
  "pm2 stop server" 停止指定进程（保留配置）
- 重启进程
  "pm2 restart server" 零停机重载（适用服务更新）
- 监控资源
  "pm2 monit" 实时显示 CPU/内存
- 删除进程
  "pm2 delete server" 彻底移除进程

### 6.服务器后端地址远程

前端页面需访问数据查询服务 url 地址" 形如 `http://服务器ip:3000`。

若要在公网使用，需将本地部署服务公开到公网，可以用 路由器端口映射 或 内网穿透(比如有公网 IP 可使用端口映射+DDNS，或使用反向代理 [ngrok](https://ngrok.com/) 、[节点小宝](https://iepose.com/)、[ZeroNews](https://www.zeronews.cc/) 都有免费版；[花生壳](https://console.hsk.oray.com/) 9 块 9 永久用，每月免费 1G 流量)
或使用 Zerotier tailscale 之类的的方式实现。具体操作不属于本文范畴，请自行搜索。
