# 蓝牙防丢 ST17H66主控芯片

需要芯片型号为 ST17H66，比如 ST17H66B，但不能是 ST17H66T，ST17H66T 属于出厂固化程序的 OTP 芯片，无法二次写入。

ST17H66T，属于出厂固化程序的 OTP 芯片，无法刷机。

# 刷机硬件工具
- usb转ttl ch340 编程器模块  
- 外部 3.3V 电源  
刷固件时，ST17H66 需要的电流较大（500mA），usb转ttl 刷机模块的输出电流不够大，刷机会失败。任意 3.3V 电源方案都可以，比如 AMS1117 稳压板，要能支持超过500mA电流。如下图是某宝的 2 种电源稳压板

# 刷机接线
ST17H66 成品或最小系统板里面的电路板，一般有 GND, 3.3V，P9, P10 几个焊盘
- 将 P10 焊盘连接到 ch340 的 TXD，将 P9 焊盘连接到 ch340 的 RXD
- 启动 flash 脚本
- 将 GND 焊盘（或纽扣电池座的负极弹片上，面积大，好焊接）连接到外部电源的负极(GND), 将 3.3V焊盘（或纽扣电池座的正极弹片上） 连接至外部 3.3V 电源的正极(VCC/VOUT)。此时外部 3.3V 电源先不要通电


# 下载刷机脚本和固件
- 前往 [Lenze_ST17H66](https://github.com/biemster/FindMy/tree/main/Lenze_ST17H66) 下载所需固件(FindMy.hex)和刷机脚本(flash_st17h66.py)  

- 因为Linux和Windows设备树的差异，如果在 windows 系统上刷机，需要修改 `flash_st17h66.py` 的一个地方，
如把flash_st17h66.py文件里面端口`/dev/ttyUSB0`改为`COM13`。
具体 COM 口数值需要电路板连接好 ch340 模块后，将 ch340 模块插到电脑 USB 口，然后去`计算机-右键管理-设备管理器`里面看

# 刷机
- ch340 模块插到电脑 USB 口。  
此时先不要启动外部 3.3V 电源

- 执行本仓库keygen目录下的 `python3 generate_keys.py` 来生成密钥对(在 `keys/` 目录下)。(注意: 必须安装依赖 `cryptography``filelock`. 用 `pip3 install cryptography filelock` 命令安装)
   Windows 或 Linux 下都可以执行。

- 管理员身份运行 CMD（否则 USB 通信不起作用）
使用以下命令启动刷机脚本
`python3 flash_st17h66.py <your base64 adv key>`  
其中 `<your base64 adv key>` 填入上一步密钥文件 `xxxx.keys` 里面的 `Advertisement key`

- 启动刷机脚本后，启动外部 3.3V 电源，直到刷机完成。 
刷机成功的 cmd 界面如下图

- 为了测试 ST17H66最小系统板 是否成功刷成定位标签，推荐使用NRF Connect ，查看蓝牙广播，除此使用需要把上面的filter 选项去掉，避免无法查看信号。

- 也可以使用 [AirGuard app](https://github.com/seemoo-lab/AirGuard/releases) 扫描 Apple/FindMy 设备，
点击 Locate Tracker 还能看信号强度，根据蓝牙信号强度确定标签的室内位置（如果你有很多定位标签分不清的话，这个功能很实用）

# 注意
- 另一个固件  
[pvvx/THB2](https://github.com/pvvx/THB2?tab=readme-ov-file#firmware) 的 ST17H66 固件(BOOT_KEY2_v20.hex)，大家可以尝试。此固件可OTA更新、配置广播间隔、广播电池电量，还能让刷完机的 iTag 发出声音(使用 [PHY62x2BTHome.html](https://pvvx.github.io/THB2/web/PHY62x2BTHome.html))。  
编程器硬件连接同上，具体刷机脚本和 OTA 配置页面的使用看此作者仓库 readme。

- ST17H66 目前为止能找到的固件都只支持单密钥。  
也就是定位标签运行过程中只有一个蓝牙 Mac 地址（密钥其实就是加密了的 Mac 地址），而多密钥就是定位标签可以定时更换 Mac 地址(称为滚动密钥，苹果 AirTag 就是这样)。经过我的测试，如果单密钥的定位标签和某台 iPhone 每天都会在同一个地方相遇，第一天 iPhone 会报告标签的位置，后面一两天可能这台 iPhone 就不会报告标签的位置了，即使 iPhone 和定位标签这一两天内并不在一个地方。我猜这与密钥轮换有关，带密钥轮换机制的标签，其位置会被更频繁的上报，即位置更新更频繁，更能被精确定位。  
因此单密钥的固件，位置更新频率和精度比较差



# 参考
- [Low cost Apple-Airtag clones](https://github.com/biemster/FindMy/issues/14) 
